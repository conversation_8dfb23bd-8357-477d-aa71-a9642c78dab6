// preload.js ,
const { ipc<PERSON>ender<PERSON> } = require('electron')

console.log("Preload script is loading...");

Number.prototype.zeroPad =
  Number.prototype.zeroPad ||
  function (base) {
    var nr = this,
      len = String(base).length - String(nr).length + 1;
    return len > 0 ? new Array(len).join("0") + nr : nr;
  };

// These functions are now handled in the main process


ipcRenderer.on('msg-to-preload', (event, message) => {
  console.log(message);
  // 可以將此消息傳遞到渲染進程
});
ipcRenderer.on('update-available', (event, newVersion) => {
  window.postMessage({ type: 'update-available', newVersion }, '*');
});
ipcRenderer.on('download-progress', (event, progress) => {
  window.postMessage({ type: 'download-progress', progress }, '*');
});
ipcRenderer.on('update-downloaded', (event) => {
  window.postMessage({ type: 'update-downloaded' }, '*');
});


// 立即創建 registerFuncs 對象，不等待 DOMContentLoaded
const registerFuncs = {};

function initializeRegisterFuncs() {
  registerFuncs.currentVersion = async () => {
    return await ipcRenderer.invoke('get-app-version');
  };
  registerFuncs.appendText = async (text) => {
    return await ipcRenderer.invoke('append-text', text);
  };
  registerFuncs.getFilename = async (temppath) => {
    return await ipcRenderer.invoke('get-filename', temppath);
  };
  registerFuncs.studioPaths = async (src, p) => {
    return await ipcRenderer.invoke('studio-paths', src, p);
  };
  registerFuncs.compareExt = async (temppath, ext) => {
    return await ipcRenderer.invoke('compare-ext', temppath, ext);
  };
  registerFuncs.isDirectory = async (temppath) => {
    return await ipcRenderer.invoke('is-directory', temppath);
  };
  registerFuncs.readXML = async (temppath) => {
    return await ipcRenderer.invoke('read-xml', temppath);
  };
  registerFuncs.isSource = async (mainpath, subpath) => {
    return await ipcRenderer.invoke('is-source', mainpath, subpath);
  };
  // registerFuncs.hasNametable = (temppath) => {
  //   var dirpath = path.dirname(temppath);
  //   var p = fs.readdirSync(dirpath).find((name) => path.parse(name).name == "_exportdetail");
  //   if (p) {
  //     var filepath = path.join(dirpath, p);
  //     var file = fs.readFileSync(filepath, "utf-8");
  //     try {
  //       JSON.parse(file);
  //     } catch (e) {
  //       return false;
  //     }
  //     var obj = JSON.parse(file);
  //     if (obj.nameTable && Object.keys(obj.nameTable).length > 0) {
  //       return true;
  //     } else {
  //       return false;
  //     }
  //   } else {
  //     return false;
  //   }
  // };
  registerFuncs.getFolderFiles = async (folderPath) => {
    return await ipcRenderer.invoke('get-folder-files', folderPath);
  };
  registerFuncs.selectFolder = async () => {
    const result = await ipcRenderer.invoke('select-folder');
    return result || "";
  };
  registerFuncs.selectFile = async () => {
    const result = await ipcRenderer.invoke('select-file');
    return result || "";
  };
  registerFuncs.saveDroppedFile = async (fileName, fileBuffer) => {
    const result = await ipcRenderer.invoke('save-dropped-file', fileName, fileBuffer);
    return result || "";
  };
  registerFuncs.openFolder = async (p) => {
    return await ipcRenderer.invoke('open-folder', p);
  };
  registerFuncs.getImgSize = async (p) => {
    return await ipcRenderer.invoke('get-img-size', p);
  };
  // registerFuncs.testPromise = () => {
  //   return new Promise((resolve, reject) => {
  //     setTimeout(() => {
  //       console.log("done");
  //       resolve(true);
  //     }, 1000)
  //   });
  // }

  registerFuncs.getDirLength = async (dir) => {
    return await ipcRenderer.invoke('get-dir-length', dir);
  };

  registerFuncs.ExchangeImage = async function (desPath, newPath) {
    return await ipcRenderer.invoke('exchange-image', desPath, newPath);
  }

  registerFuncs.isChildOf = async function (parent, child) {
    return await ipcRenderer.invoke('is-child-of', parent, child);
  }
  registerFuncs.updateApp = () => {
    ipcRenderer.send('startUpdate');
  };
  registerFuncs.compareImageSimilarity = async function (imagePath1, imagePath2, customWeights) {
    return await ipcRenderer.invoke('compare-image-similarity', imagePath1, imagePath2, customWeights);
  };
  // registerFuncs.convertProjFile = (desPath, filePath, version) => {
  //   return new Promise((resolve, reject) => {
  //   });
  // }
  // 直接設置 window 屬性（因為 contextIsolation 已禁用）
  window.registerFuncs = registerFuncs;
  console.log("registerFuncs set on window");
}

// 立即初始化
initializeRegisterFuncs();

// 也在 DOMContentLoaded 時再次確保初始化
window.addEventListener("DOMContentLoaded", () => {
  if (!window.registerFuncs) {
    initializeRegisterFuncs();
  }
  console.log("registerFuncs status:", window.registerFuncs ? "available" : "undefined");
});
