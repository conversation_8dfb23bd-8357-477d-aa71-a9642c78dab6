<template>
  <div class="main-page">
    <div class="header">
      <div class="row-container">
        <i class="fa fa-folder-open" aria-hidden="true" style="margin-right: 10px; color: white"></i>
        <label class="basic-font" style="width: 80px; color: white; font-size: 14px; margin-right: 5px; text-align: left">資料夾</label>
        <input class="input-area" type="text" style="flex-grow: 1; margin-right: 10px" readonly="true" placeholder="放入資料夾" v-model="desPath" />
        <button class="small basic-font" style="margin-right: 5px" @click.prevent="clickTarget(false)">選擇</button>
        <button class="small basic-font" @click.prevent="clearTarget">清空</button>
      </div>
      <!-- <div class="row-container">
        <i class="fa fa-folder-open" aria-hidden="true" style="margin-right: 10px; color: white"></i>
        <label class="basic-font" style="width: 100px; color: white; font-size: 14px; margin-right: 10px">導出資料夾</label>
        <p style="flex-grow: 1"></p>
        <button class="small basic-font" style="margin-right: 5px" @click.prevent="clickTarget(false)">_tp_選擇</button>
        <button class="small basic-font" @click.prevent="clearTarget">_tp_清空</button>
      </div> -->
      <div class="row-container">
        <i class="fa fa-folder-open" aria-hidden="true" style="margin-right: 10px; color: white"></i>
        <label class="basic-font" style="width: 80px; color: white; font-size: 14px; margin-right: 5px; text-align: left">索引檔</label>
        <input class="input-area" type="text" style="flex-grow: 1; margin-right: 10px" readonly="true" placeholder="放入Prefab或Csd可標註被使用的圖片" v-model="indexFile" />
        <button class="small basic-font" style="margin-right: 5px" @click.prevent="clickSource">選擇</button>
        <button class="small basic-font" @click.prevent="clearIndex">清空</button>
      </div>
      <div class="row-container">
        <i class="fa fa-folder-open" aria-hidden="true" style="margin-right: 10px; color: white"></i>
        <label class="basic-font" style="width: 80px; color: white; font-size: 14px; margin-right: 5px; text-align: left">參考資料夾</label>
        <input class="input-area" type="text" style="flex-grow: 1; margin-right: 10px" readonly="true" placeholder="選擇參考圖片資料夾進行自動匹配" v-model="referencePath" />
        <button class="small basic-font" style="margin-right: 5px" @click.prevent="clickReference">選擇</button>
        <button class="small basic-font" style="margin-right: 5px" @click.prevent="clearReference">清空</button>
        <button class="small basic-font" style="margin-right: 5px" @click.prevent="autoMatch" :disabled="!referencePath || !desPath || isMatching">
          {{ isMatching ? '匹配中...' : '匹配' }}
        </button>
        <button class="small basic-font" style="margin-right: 5px" @click.prevent="toggleWeightSettings">
          {{ showWeightSettings ? '隱藏設定' : '權重設定' }}
        </button>
      </div>
    </div>
    <div class="container">
      <div class="container2" style="width: 100%">
        <div class="board">
          <span style="color: white; user-select: none">
            顯示資料夾
            <!-- <vue-dropdown :folderList="folderList" :rootLength="rootLength" style="margin-right: 20px" @change="setImgCounter()"></vue-dropdown> -->
            <select class="base basic-font clickable" v-model="folderList.selected" @change="setImgCounter">
              <option class="basic-font" v-for="(option, index) in folderList.options" :key="index" v-bind:value="option.value">
                <span v-for="n in Math.max(0, DirLength(option.value) || 0)" :key="n">■</span>
                {{ option.text }}
              </option>
            </select>
            共
            <span class="basic-font">{{ imgCounter }}</span>
            張圖片
            <span class="basic-font" style="color: #ffde37">（已替換{{ changeFiles.length }}張）</span>
          </span>
          <!-- <vue-dropdown v-model="folderIndex" :options="folderList"></vue-dropdown> -->
          <!-- <span class="basic-font" style="color: white">資料夾選擇：</span> -->
          <!-- <span class="basic-font" style="color: white">資料夾總覽</span> -->
          <!-- <i class="fas fa-retweet-alt"></i> -->
          <i class="fa fa-retweet-alt remove basic-font" aria-hidden="true" style="color: white"></i>
        </div>
        <div ref="main" class="content" :style="{ height: '100%', backgroundColor: bgColor }" @drop.stop="dropFile" @dragover="dragover" @dragenter="allowDrop" multiple>
          <div class="basic-font drag-hint" v-show="desPath == ''">
            <span class="center">放置資料夾</span>
          </div>
          <!-- <div v-for="(p, index) in convertFiles" :key="index">
            <img :src="p" style="max-width: 80px; max-height: 80px" />
          </div> -->
          <!-- <convert-item v-for="(child, index) in convertFiles" :key="index" :convertFile="child"></convert-item> -->
          <exchange-item v-for="(child, index) in filteredConvertFiles" :key="index" :filePath="child.path" :isShow="true"></exchange-item>
          <!-- child.dirname.length == folderList.selected.length -->
        </div>
      </div>
      <!-- 相似度權重設定區域 -->
      <div v-show="showWeightSettings" class="weight-settings-panel">
        <div class="weight-row">
          <label class="basic-font weight-label">直方圖權重:</label>
          <input type="range" min="0" max="1" step="0.1" v-model.number="similarityWeights.histogram" class="weight-slider" />
          <span class="basic-font weight-value">{{ similarityWeights.histogram.toFixed(1) }}</span>
        </div>
        <div class="weight-row">
          <label class="basic-font weight-label">像素權重:</label>
          <input type="range" min="0" max="1" step="0.1" v-model.number="similarityWeights.pixel" class="weight-slider" />
          <span class="basic-font weight-value">{{ similarityWeights.pixel.toFixed(1) }}</span>
        </div>
        <div class="weight-row">
          <label class="basic-font weight-label">結構權重:</label>
          <input type="range" min="0" max="1" step="0.1" v-model.number="similarityWeights.structure" class="weight-slider" />
          <span class="basic-font weight-value">{{ similarityWeights.structure.toFixed(1) }}</span>
        </div>
        <div class="weight-row">
          <label class="basic-font weight-label">相似度閾值:</label>
          <input type="range" min="0.1" max="1.0" step="0.05" v-model.number="similarityThreshold" class="weight-slider" />
          <span class="basic-font weight-value">{{ (similarityThreshold * 100).toFixed(0) }}%</span>
        </div>
        <div class="weight-row">
          <button class="small basic-font" @click.prevent="resetWeights" style="margin-right: 5px">重置預設</button>
          <button class="small basic-font" @click.prevent="normalizeWeights">正規化</button>
          <span class="basic-font weight-sum" :class="{ 'weight-warning': weightSum !== 1.0 }">
            權重總和: {{ weightSum.toFixed(1) }}
          </span>
        </div>
      </div>
    </div>
    <div class="footer">
      <div class="row-container" style="justify-content: flex-end; align-items: center">
        <!-- <div style="wide:200px">
          <select v-model="versionPick">
            <option selected>3.10.0.0</option>
            <option>2.3.2.3</option>
          </select>
          <span>CSD輸出版本: {{ versionPick }}</span>
        </div> -->
        <div style="padding: 10px; display: flex; align-items: center; align-self: self-start">
          <span class="basic-font" style="font-weight: bold; font-size: 18px">背景顏色</span>
          <input class="clickable" style="outline: transparent; width: 50px; height: 25px; text-align: center" type="color" v-model="bgColor" />
          <!-- <colorPicker v-model="bgColor"></colorPicker> -->
          <!-- <span>Creator版本: </span>
          <input type="radio" id="342" value="342" v-model="versionPick" style="margin-right: 5px" />
          <label for="342" style="margin-right: 10px">3.4.2版本</label>
          <input type="radio" id="362" value="362" v-model="versionPick" style="margin-right: 5px" />
          <label for="362">3.6.2版本</label> -->
        </div>
        <div style="flex-grow: 1"></div>
        <div id="exportLoader" class="loader" style="margin-right: 10px; display: none"></div>
        <button id="exportBtn" class="medium basic-font red" @click.stop="handleResetSwap" style="margin-right: 10px">重置</button>
        <button id="exportBtn" class="medium basic-font" @click.stop="swapStart">開始</button>
      </div>
    </div>
    <div id="checkmenu" class="modal">
      <div class="modal-content2" style="height: 180px; width: 240px; border-radius: 5px">
        <span class="modal-text basic-font"
          ><i class="fa fa-exclamation-triangle" aria-hidden="true" style="color: #ffd30a"></i>
          注意
          <i class="fa fa-exclamation-triangle" aria-hidden="true" style="color: #ffd30a"></i
        ></span>
        <div style="display: table; height: 45px; width: 80%; overflow: hidden; margin-top: 10px">
          <div style="display: table-cell; vertical-align: middle">
            <div class="basic-font" style="text-align: center; font-weight: bold">確認開始？</div>
          </div>
        </div>
        <div>
          <button class="medium" style="margin-top: 15px; margin-right: 10px" @click.stop="closeMenu(true)">確認</button>
          <button class="medium" style="margin-top: 15px; margin-left: 10px" @click.stop="closeMenu(false)">取消</button>
        </div>
      </div>
    </div>
    <div id="block" class="modal"></div>
  </div>
</template>
<style scoped>
.base {
  background-color: #0d2d50;
  border-radius: 3px;
  /* padding: 2px 4px; */
  -webkit-appearance: menulist !important; /* override vuetify style */
  -moze-appearance: menulist !important; /* override vuetify style */
  appearance: menulist !important; /* override vuetify style */
  /* font-weight: bold; */
  /* background-color: black; */
  color: rgb(252, 221, 44);
  outline: none;
}
.base option {
  font-size: 14px;
  background-color: #123c69;
  color: white;
}

/* 相似度權重設定面板樣式 */
.weight-settings-panel {
  background-color: #1a3a5c;
  border: 1px solid #2d5a87;
  border-radius: 5px;
  padding: 15px;
  margin: 10px 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.weight-row {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  gap: 10px;
}

.weight-label {
  color: white;
  font-size: 14px;
  width: 100px;
  text-align: left;
}

.weight-slider {
  flex: 1;
  height: 6px;
  background: #2d5a87;
  border-radius: 3px;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
}

.weight-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  background: #fcdd2c;
  border-radius: 50%;
  cursor: pointer;
  border: 2px solid #fff;
}

.weight-slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  background: #fcdd2c;
  border-radius: 50%;
  cursor: pointer;
  border: 2px solid #fff;
}

.weight-value {
  color: #fcdd2c;
  font-size: 14px;
  font-weight: bold;
  width: 40px;
  text-align: center;
}

.weight-sum {
  color: #fcdd2c;
  font-size: 14px;
  margin-left: 15px;
}

.weight-warning {
  color: #ff6b6b !important;
  font-weight: bold;
}
</style>
<script>
import "@/assets/style.css";
// import ConvertItem from "@/components/ConvertItem.vue";
// import VueDropdown from "@/components/VueDropdown.vue";
import ExchangeItem from "@/components/ExchangeItem.vue";

export default {
  components: { ExchangeItem },
  created() {
    console.log("Page component created");
  },
  mounted() {
    console.log("Page component mounted");
    console.log("window.registerFuncs:", window.registerFuncs);
    if (!window.registerFuncs) {
      console.error("registerFuncs is not available in Page component");
    }
  },
  computed: {
    filteredConvertFiles() {
      if (!this.convertFiles || this.convertFiles.length === 0) {
        return [];
      }
      if (this.folderList.selected === this.desPath) {
        return this.convertFiles;
      }
      return this.convertFiles.filter(child => {
        const cacheKey = `${this.folderList.selected}|${child.dirname}`;
        return this.isChildOfCache[cacheKey] === true;
      });
    },
    weightSum() {
      return this.similarityWeights.histogram + this.similarityWeights.pixel + this.similarityWeights.structure;
    }
  },
  methods: {
    closeMenu: async function (check) {
      this.$el.querySelector("#checkmenu").style.display = "none";
      if (check) {
        this.$el.querySelector("#exportLoader").style.display = "block";
        this.$el.querySelector("#block").style.display = "block";
        await new Promise((r) => setTimeout(r, 200));
        for (var i = 0; i < this.changeFiles.length; i++) {
          await window.registerFuncs.ExchangeImage(this.changeFiles[i].des, this.changeFiles[i].new);
          // this.convertFiles[i].complete = await window.registerFuncs.testPromise();
        }
        await new Promise((r) => setTimeout(r, 300));
        await this.resetSwap();
        this.$el.querySelector("#exportLoader").style.display = "none";
        this.$el.querySelector("#block").style.display = "none";
        this.$bus.$emit("show-warn", "轉換完成！", true);
      }
    },
    swapStart: function () {
      if (this.convertFiles.length > 0 && this.desPath !== "") {
        // this.startConvert = true;
        this.$el.querySelector("#checkmenu").style.display = "block";
        // window.registerFuncs.swapProjFiles(this.convertFiles);
      } else if (!this.desPath) {
        this.clickTarget(true);
      }
      // console.log("start");
    },
    dragover: function (ev) {
      ev.preventDefault();
    },
    allowDrop: function (ev) {
      ev.preventDefault();
      // console.log("outter_allow");
    },
    // endDrop: function (ev) {
    //   ev.preventDefault();
    //   // console.log("outter_end");
    // },
    dropFile: async function (ev) {
      // console.log("outter_drop");
      ev.preventDefault();
      var files = ev.dataTransfer.files;
      if (files.length > 0) {
        if (await window.registerFuncs.isDirectory(files[0].path)) {
          var temp = files[0].path;
          if (temp) {
            this.desPath = files[0].path;
            await this.resetSwap();
            // this.convertFiles = await window.registerFuncs.getFolderFiles(this.desPath);
            // this.changeFiles = [];
            // console.log(this.convertFiles);
            // fs.readdirSync(this.desPath).forEach((file) => {
            //   console.log(file);
            // });
            //handle directory
          }
        } else if ((await window.registerFuncs.compareExt(files[0].path, ".prefab")) || (await window.registerFuncs.compareExt(files[0].path, ".csd"))) {
          this.indexFile = files[0].path;
        } else {
          this.$bus.$emit("show-warn", "檔案格式錯誤！", false);
        }
      }
      // var files = ev.dataTransfer.files;
      // for (var i = 0; i < files.length; i++) {
      //   if (window.registerFuncs.compareExt(files[i].path, ".prefab") || window.registerFuncs.compareExt(files[i].path, ".csd")) {
      //     var filename = window.registerFuncs.getFilename(files[i].path);
      //     if (!this.convertFiles.find((el) => el.path == files[i].path)) {
      //       this.convertFiles.push({
      //         path: files[i].path,
      //         name: filename,
      //         complete: false,
      //       });
      //     } else {
      //       this.$bus.$emit("show-warn", "該檔案已在駐列內！", false);
      //     }
      //   } else {
      //     this.$bus.$emit("show-warn", "檔案格式錯誤！", false);
      //   }
      // }
      // console.log("aa");
    },
    clickTarget: async function (recheck) {
      if (!window.registerFuncs || !window.registerFuncs.selectFolder) {
        console.error("registerFuncs.selectFolder is not available");
        this.$bus.$emit("show-warn", "系統功能尚未載入，請稍後再試", false);
        return;
      }
      try {
        var temp = await window.registerFuncs.selectFolder();
        if (temp) {
          this.desPath = temp;
          await this.resetSwap();
          if (this.desPath !== "" && recheck) {
            this.swapStart();
          }
        }
      } catch (error) {
        console.error("Error in clickTarget:", error);
        this.$bus.$emit("show-warn", "選擇資料夾時發生錯誤", false);
      }
    },
    clearTarget: function () {
      // this.resetSwap();
      this.imgCounter = 0;
      this.desPath = "";
      this.rootLength = 0;
      this.convertFiles = [];
      this.changeFiles = [];
      this.folderList = {
        selected: "root",
        options: [{ text: "所有", value: "root" }],
      };
    },
    clickSource: async function () {
      var temp = await window.registerFuncs.selectFile();
      if (temp) {
        if ((await window.registerFuncs.compareExt(temp, ".ccs")) || (await window.registerFuncs.compareExt(temp, ".prefab"))) {
          // var filename = await window.registerFuncs.getFilename(temp);
          // if (!this.convertFiles.find((el) => el.path == temp)) {
          //   this.convertFiles.push({
          //     path: temp,
          //     name: filename,
          //     complete: false,
          //   });
          // } else {
          //   this.$bus.$emit("show-warn", "該檔案已在駐列內！", false);
          // }
        } else {
          this.$bus.$emit("show-warn", "檔案格式錯誤！", false);
        }
      }
    },
    isChildOf: function (parentDir, childDir) {
      const cacheKey = `${parentDir}|${childDir}`;
      if (this.isChildOfCache[cacheKey] !== undefined) {
        return this.isChildOfCache[cacheKey];
      }
      return false; // Default to false if not cached
    },
    async updateIsChildOfCache(parentDir, childDir) {
      const cacheKey = `${parentDir}|${childDir}`;
      if (this.isChildOfCache[cacheKey] === undefined) {
        this.isChildOfCache[cacheKey] = await window.registerFuncs.isChildOf(parentDir, childDir);
      }
    },
    handleResetSwap: async function () {
      await this.resetSwap();
    },
    resetSwap: async function () {
      if (this.convertFiles) {
        this.imgCounter = 0;
        this.rootLength = 0;
        this.convertFiles = [];
        this.changeFiles = [];
        this.dirLengthCache = {};
        this.isChildOfCache = {};
        if (this.desPath) {
          this.rootLength = await window.registerFuncs.getDirLength(this.desPath);
          this.convertFiles = await window.registerFuncs.getFolderFiles(this.desPath);
          this.folderList = {
            selected: this.desPath,
            options: [{ text: "所有", value: this.desPath }],
          };

          // Pre-cache directory lengths and isChildOf results
          let templist = [];
          for (var i = 0; i < this.convertFiles.length; i++) {
            let dirname = this.convertFiles[i].dirname;
            if (this.convertFiles[i].dirname != this.desPath && !templist.includes(dirname)) {
              templist.push(dirname);
              this.folderList.options.push({ text: this.convertFiles[i].foldername, value: dirname });
            }
            await this.updateDirLengthCache(dirname);
            await this.updateIsChildOfCache(this.folderList.selected, dirname);
          }

          this.setImgCounter();
        }
      }
    },
    clearIndex: function () {
      this.indexFile = "";
      // this.convertFiles = [];
    },
    clickReference: async function () {
      if (!window.registerFuncs || !window.registerFuncs.selectFolder) {
        console.error("registerFuncs.selectFolder is not available");
        this.$bus.$emit("show-warn", "系統功能尚未載入，請稍後再試", false);
        return;
      }
      try {
        var temp = await window.registerFuncs.selectFolder();
        if (temp) {
          this.referencePath = temp;
          await this.loadReferenceFiles();
        }
      } catch (error) {
        console.error("Error in clickReference:", error);
        this.$bus.$emit("show-warn", "選擇參考資料夾時發生錯誤", false);
      }
    },
    clearReference: function () {
      this.referencePath = "";
      this.referenceFiles = [];
    },
    loadReferenceFiles: async function () {
      if (!this.referencePath) return;

      try {
        const files = await window.registerFuncs.getFolderFiles(this.referencePath);
        this.referenceFiles = files.filter(file => {
          const ext = file.path.toLowerCase().split('.').pop();
          return ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'].includes(ext);
        });
        console.log(`載入了 ${this.referenceFiles.length} 個參考圖片`);
      } catch (error) {
        console.error("Error loading reference files:", error);
        this.$bus.$emit("show-warn", "載入參考圖片時發生錯誤", false);
      }
    },
    autoMatch: async function () {
      if (!this.referencePath || !this.desPath || this.convertFiles.length === 0) {
        this.$bus.$emit("show-warn", "請先選擇目標資料夾和參考資料夾", false);
        return;
      }

      if (this.referenceFiles.length === 0) {
        this.$bus.$emit("show-warn", "參考資料夾中沒有找到圖片檔案", false);
        return;
      }

      // 檢查權重總和是否為1
      const weightSum = this.getWeightSum();
      if (Math.abs(weightSum - 1.0) > 0.001) {
        this.$bus.$emit("show-warn", `權重總和必須等於 1.0，目前為 ${weightSum.toFixed(3)}。請調整權重設定。`, false);
        return;
      }

      this.isMatching = true;
      let matchCount = 0;

      try {
        for (let i = 0; i < this.convertFiles.length; i++) {
          const targetFile = this.convertFiles[i];
          // 使用用戶設定的權重
          const customWeights = {
            histogram: this.similarityWeights.histogram,
            pixel: this.similarityWeights.pixel,
            structure: this.similarityWeights.structure
          };
          let bestMatch = null;
          let bestSimilarity = 0;

          // 與所有參考圖片進行比較
          for (const refFile of this.referenceFiles) {
            try {
              const similarity = await window.registerFuncs.compareImageSimilarity(
                targetFile.path,
                refFile.path,
                customWeights
              );

              if (similarity > bestSimilarity) {
                bestSimilarity = similarity;
                bestMatch = refFile;
              }
            } catch (error) {
              console.warn(`比較圖片時發生錯誤: ${targetFile.path} vs ${refFile.path}`, error);
            }
          }

          // 使用用戶設定的相似度閾值
          if (bestMatch && bestSimilarity >= this.similarityThreshold) {
            // 觸發自動匹配事件
            this.$bus.$emit("auto-match-result", targetFile.path, bestMatch.path);
            // 同時觸發 add-change 事件來更新 changeFiles 列表
            this.$bus.$emit("add-change", targetFile.path, bestMatch.path);
            matchCount++;
            console.log(`自動匹配: ${targetFile.filename} -> ${bestMatch.filename} (相似度: ${(bestSimilarity * 100).toFixed(1)}%)`);
          }
        }

        this.$bus.$emit("show-warn", `自動匹配完成！成功匹配 ${matchCount} 個圖片 (閾值: ${(this.similarityThreshold * 100).toFixed(0)}%)`, false);
      } catch (error) {
        console.error("Auto match error:", error);
        this.$bus.$emit("show-warn", "自動匹配時發生錯誤", false);
      } finally {
        this.isMatching = false;
      }
    },
    toggleWeightSettings: function () {
      // 如果要關閉設定面板，檢查權重總和是否為1
      if (this.showWeightSettings) {
        const weightSum = this.getWeightSum();
        if (Math.abs(weightSum - 1.0) > 0.001) {
          this.$bus.$emit("show-warn", `權重總和必須等於 1.0，目前為 ${weightSum.toFixed(3)}。請調整權重或使用「自動正規化」功能。`, false);
          return; // 不關閉設定面板
        }
      }
      this.showWeightSettings = !this.showWeightSettings;
    },
    resetWeights: function () {
      this.similarityWeights = {
        histogram: 0.4,
        pixel: 0.3,
        structure: 0.3
      };
      this.similarityThreshold = 0.7;
      this.$bus.$emit("show-warn", "權重已重置為預設值", true);
    },
    normalizeWeights: function () {
      const sum = this.weightSum;
      if (sum > 0) {
        this.similarityWeights.histogram = this.similarityWeights.histogram / sum;
        this.similarityWeights.pixel = this.similarityWeights.pixel / sum;
        this.similarityWeights.structure = this.similarityWeights.structure / sum;
        this.$bus.$emit("show-warn", "權重已自動正規化", true);
      }
    },
    setImgCounter: function () {
      this.imgCounter = this.filteredConvertFiles.length;
    },
    // getImgCounter: function () {
    //   return this.refs.content.child.length;
    //   // return 10;
    // },
    DirLength: function (dir) {
      if (this.rootLength > 0 && this.dirLengthCache && this.dirLengthCache[dir] !== undefined) {
        const result = this.dirLengthCache[dir] - this.rootLength;
        return Math.max(0, result); // 確保返回非負數
      }
      return 0;
    },
    async updateDirLengthCache(dir) {
      if (!this.dirLengthCache[dir]) {
        this.dirLengthCache[dir] = await window.registerFuncs.getDirLength(dir);
      }
    },
  },

  data() {
    return {
      imgCounter: 0,
      bgColor: "#ffffff",
      rootLength: 0,
      desPath: "",
      indexFile: "",
      referencePath: "",
      convertFiles: [],
      changeFiles: [],
      referenceFiles: [],
      folderList: {
        selected: "root",
        options: [{ text: "所有", value: "root" }],
      },
      dirLengthCache: {},
      isChildOfCache: {},
      isMatching: false,
      showWeightSettings: false,
      similarityWeights: {
        histogram: 0.4,
        pixel: 0.3,
        structure: 0.3
      },
      similarityThreshold: 0.7,
    };
  },
  // mounted() {
  //   this.imgCounter = this.$refs.main.childElementCount;
  // },
  created() {
    this.$bus.$on("add-change", (desPath, newPath) => {
      this.changeFiles.push({ des: desPath, new: newPath });
    });
    this.$bus.$on("remove-change", (desPath, newPath) => {
      this.changeFiles = this.changeFiles.filter((obj) => obj.des != desPath && obj.new != newPath);
    });

    // this.$bus.$on("page1-end", () => {
    //   // this.convertFiles = [];
    //   this.$el.querySelector("#block").style.display = "none";
    //   if (this.openFolder) {
    //     // window.registerFuncs.openFolder(this.desPath);
    //     // console.log("open");
    //   }
    // });
  },
  beforeDestroy() {
    // this.$bus.$off("delete-item");
    // this.$bus.$off("page1-end");
    this.$bus.$off("add-change");
    this.$bus.$off("remove-change");
  },
};
</script>
