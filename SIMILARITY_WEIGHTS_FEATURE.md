# 相似度權重調整功能

## 功能概述

新增了可以讓使用者調整相似度權重的功能，允許用戶自定義圖片相似度比較算法的權重配置。

## 新增功能

### 1. 相似度設定面板

- 在參考資料夾選擇區域新增了「相似度設定」按鈕
- 點擊後會顯示/隱藏權重設定面板

### 2. 權重調整控制項

#### 三種算法權重調整：
- **直方圖權重** (histogram): 控制直方圖相似度算法的權重 (0.0 - 1.0)
- **像素權重** (pixel): 控制像素級相似度算法的權重 (0.0 - 1.0)  
- **結構權重** (structure): 控制結構相似性算法的權重 (0.0 - 1.0)

#### 相似度閾值調整：
- **相似度閾值**: 控制自動匹配的最低相似度要求 (10% - 100%)

### 3. 權重管理功能

#### 重置預設按鈕：
- 將所有權重重置為預設值：
  - 直方圖權重: 0.4
  - 像素權重: 0.3
  - 結構權重: 0.3
  - 相似度閾值: 70%

#### 自動正規化按鈕：
- 自動調整三個算法權重，使其總和等於 1.0
- 保持各權重之間的相對比例

#### 權重總和顯示：
- 實時顯示當前三個算法權重的總和
- 當總和不等於 1.0 時會以紅色警告顯示

## 使用方法

### 基本使用流程：

1. **選擇目標資料夾**：選擇包含要替換圖片的資料夾
2. **選擇參考資料夾**：選擇包含參考圖片的資料夾  
3. **調整相似度設定**（可選）：
   - 點擊「相似度設定」按鈕打開設定面板
   - 使用滑桿調整各算法權重
   - 調整相似度閾值
   - 確保權重總和為 1.0（可使用自動正規化功能）
4. **執行自動匹配**：點擊「匹配」按鈕開始自動匹配

### 權重調整建議：

#### 不同場景的權重配置：

**一般圖片匹配**（預設）：
- 直方圖權重: 0.4
- 像素權重: 0.3  
- 結構權重: 0.3

**注重顏色分佈的匹配**：
- 直方圖權重: 0.6
- 像素權重: 0.2
- 結構權重: 0.2

**注重細節匹配**：
- 直方圖權重: 0.2
- 像素權重: 0.5
- 結構權重: 0.3

**注重結構相似性**：
- 直方圖權重: 0.2
- 像素權重: 0.2
- 結構權重: 0.6

## 技術實現

### 前端實現：
- 在 `Page.vue` 中新增權重設定UI組件
- 使用 Vue.js 的響應式數據綁定
- 實時計算和顯示權重總和
- 權重驗證和正規化功能

### 後端實現：
- 修改 `autoMatch` 方法使用用戶自定義權重
- 權重參數傳遞到相似度計算算法
- 保持與現有 `ImageSimilarityCalculator` 的兼容性

### 數據結構：
```javascript
similarityWeights: {
  histogram: 0.4,    // 直方圖權重
  pixel: 0.3,        // 像素權重  
  structure: 0.3     // 結構權重
},
similarityThreshold: 0.7  // 相似度閾值
```

## 注意事項

1. **權重總和必須等於 1.0**：系統會檢查權重總和，如果不等於 1.0 會顯示警告並阻止匹配
2. **權重範圍**：每個權重值必須在 0.0 到 1.0 之間
3. **閾值範圍**：相似度閾值必須在 0.1 到 1.0 之間（10% 到 100%）
4. **性能考慮**：不同的權重配置可能會影響匹配速度和準確性

## 未來改進

1. 預設權重配置模板
2. 權重配置的保存和載入功能
3. 批量測試不同權重配置的效果
4. 更詳細的相似度分析報告
